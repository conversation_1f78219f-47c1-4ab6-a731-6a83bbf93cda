{"name": "@sveltejs/vite-plugin-svelte-inspector", "version": "5.0.1", "license": "MIT", "author": "dominikg", "files": ["src", "types"], "type": "module", "types": "types/index.d.ts", "exports": {".": {"types": "./types/index.d.ts", "default": "./src/index.js"}}, "engines": {"node": "^20.19 || ^22.12 || >=24"}, "repository": {"type": "git", "url": "git+https://github.com/sveltejs/vite-plugin-svelte.git", "directory": "packages/vite-plugin-svelte-inspector"}, "keywords": ["vite-plugin", "vite plugin", "vite", "svelte"], "bugs": {"url": "https://github.com/sveltejs/vite-plugin-svelte/issues"}, "homepage": "https://github.com/sveltejs/vite-plugin-svelte#readme", "dependencies": {"debug": "^4.4.1"}, "peerDependencies": {"@sveltejs/vite-plugin-svelte": "^6.0.0-next.0", "svelte": "^5.0.0", "vite": "^6.3.0 || ^7.0.0"}, "devDependencies": {"@types/debug": "^4.1.12", "svelte": "^5.38.0", "vite": "^7.1.1"}, "scripts": {"check:publint": "publint --strict", "check:types": "tsc --noEmit", "generate:types": "dts-buddy -m \"@sveltejs/vite-plugin-svelte-inspector:src/public.d.ts\""}}