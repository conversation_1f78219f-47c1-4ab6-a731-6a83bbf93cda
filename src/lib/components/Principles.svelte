<section id="principles">
  <div class="container">
    <h1>Principles</h1>
    <ul class="principles-grid">
      <li class="principle-item">
        <h2>No algorithms</h2>
        <p class="principle-description">
          Your timeline shows posts in chronological order, giving you control
          over what you see without hidden manipulation.
        </p>
      </li>
      <li class="principle-item">
        <h2>Follow feeds, not people</h2>
        <p class="principle-description">
          Discover content through curated feeds that match your interests,
          creating a more focused and intentional browsing experience.
        </p>
      </li>
      <li class="principle-item">
        <h2>Create your own feeds</h2>
        <p class="principle-description">
          Build custom feeds tailored to your specific interests and share them
          with others who share your passions.
        </p>
      </li>
      <li class="principle-item">
        <h2>No bots</h2>
        <p class="principle-description">
          Engage with real people in authentic conversations, free from
          automated accounts and artificial engagement.
        </p>
      </li>
      <li class="principle-item">
        <h2>Public benefit, owned by employees</h2>
        <p class="principle-description">
          A platform built for users, not shareholders, ensuring decisions
          prioritize community well-being over profit maximization.
        </p>
      </li>
    </ul>
  </div>
</section>

<style>
  section {
    padding: 6rem 0;
    background: white;
  }

  h1 {
    font-size: 2.5rem;
    font-weight: bold;
    color: #1f2937;
    text-align: center;
    margin-bottom: 3rem;
  }

  .principles-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    max-width: 900px;
    margin: 0 auto;
  }

  .principle-item {
    text-align: center;
    padding: 1rem;
    transition: all 0.2s ease;
  }

  .principle-item h2 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 0.75rem 0;
    line-height: 1.4;
  }

  .principle-description {
    font-size: 0.95rem;
    line-height: 1.6;
    color: #6b7280;
    margin: 0;
    transition: color 0.2s ease;
  }

  .principle-item:hover .principle-description,
  .principle-item:focus-within .principle-description {
    color: #374151;
  }
</style>
