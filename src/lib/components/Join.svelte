<script lang="ts">
  let email = $state("");

  function handleSubmit(event: Event) {
    event.preventDefault();
    // Handle form submission here
    console.log("Email submitted:", email);
    // You can add actual form handling logic here
  }
</script>

<section id="join" class="join">
  <div class="join-container">
    <h2 class="section-title">Sound appealing?</h2>
    <p class="join-subtitle">
      <PERSON><PERSON><PERSON> will be invite-only for the foreseeable future. You can request to
      join here:
    </p>

    <form class="email-form" onsubmit={handleSubmit}>
      <div class="form-group">
        <input
          type="email"
          bind:value={email}
          placeholder="Enter your email address"
          class="email-input"
          required
        />
        <button type="submit" class="submit-button"> Request Invite </button>
      </div>
    </form>

    <p class="privacy-note">
      Your email will only be used to inform you of an invite.
    </p>
  </div>
</section>

<style>
  .join {
    padding: 6rem 0;
    background: white;
  }

  .join-container {
    max-width: 600px;
    margin: 0 auto;
    padding: 0 2rem;
    text-align: center;
  }

  .section-title {
    font-size: 2.5rem;
    font-weight: bold;
    color: #1f2937;
    margin-bottom: 1.5rem;
  }

  .join-subtitle {
    font-size: 1.25rem;
    color: #6b7280;
    line-height: 1.6;
    margin-bottom: 3rem;
  }

  .email-form {
    margin-bottom: 1rem;
  }

  .form-group {
    display: flex;
    gap: 1rem;
    max-width: 400px;
    margin: 0 auto;
  }

  .email-input {
    flex: 1;
    padding: 0.75rem 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.2s;
  }

  .email-input:focus {
    outline: none;
    border-color: #3b82f6;
  }

  .submit-button {
    padding: 0.75rem 1.5rem;
    background: #1f2937;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
  }

  .submit-button:hover {
    background: #374151;
  }

  .privacy-note {
    font-size: 0.9rem;
    color: #9ca3af;
    margin: 0;
  }

  @media (max-width: 768px) {
    .join {
      padding: 4rem 0;
    }

    .join-container {
      padding: 0 1rem;
    }

    .section-title {
      font-size: 2rem;
    }

    .join-subtitle {
      font-size: 1.1rem;
      margin-bottom: 2rem;
    }

    .form-group {
      flex-direction: column;
      max-width: 300px;
    }

    .submit-button {
      padding: 0.875rem 1.5rem;
    }
  }
</style>
